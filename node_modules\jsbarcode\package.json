{"name": "jsbarcode", "version": "3.12.1", "description": "JsBarcode is a customizable barcode generator with support for multiple barcode formats.", "main": "./bin/JsBarcode.js", "scripts": {"test": "gulp babel && node_modules/mocha/bin/mocha test/node/ -R spec", "coveralls": "NODE_ENV=test YOURPACKAGE_COVERAGE=1 ./node_modules/.bin/mocha test/node/ --require blanket --reporter mocha-lcov-reporter | ./node_modules/coveralls/bin/coveralls.js", "coverage": "./node_modules/.bin/mocha test/node/ -r blanket -R html-cov > test/coverage.html", "build": "gulp compile"}, "repository": {"type": "git", "url": "git+https://github.com/lindell/JsBarcode.git"}, "keywords": ["barcode", "canvas", "code128", "upc", "ean", "itf", "msi", "pharmacode"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/lindell/JsBarcode/issues"}, "homepage": "https://github.com/lindell/JsBarcode#readme", "devDependencies": {"babel-cli": "^6.24.1", "babel-core": "^6.24.1", "babel-loader": "^7.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "6.24.1", "blanket": "^1.2.3", "canvas": "^2.6.1", "coveralls": "^3.1.0", "gulp": "^4.0.2", "gulp-babel": "^6.1.2", "gulp-bump": "^3.2.0", "gulp-clean": "^0.4.0", "gulp-concat": "^2.6.1", "gulp-eslint": "^6.0.0", "gulp-git": "^2.10.1", "gulp-header": "^2.0.9", "gulp-rename": "^2.0.0", "gulp-uglify": "^3.0.2", "gulp4-run-sequence": "^1.0.0", "gzip-size": "^5.1.1", "mocha": "^8.1.1", "mocha-lcov-reporter": "^1.3.0", "publish-release": "^1.6.1", "request": "^2.88.2", "webpack": "^4.44.1", "webpack-stream": "^5.2.1", "xmldom": "^0.1.31"}, "typings": "./jsbarcode.d.ts", "config": {"blanket": {"pattern": ["JsBarcode.js", "barcodes"], "data-cover-never": ["GenericBarcode", "node_modules"]}}}